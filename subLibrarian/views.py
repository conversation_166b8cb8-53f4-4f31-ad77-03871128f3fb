from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.models import User, Group
from django.contrib.auth import login, logout, authenticate
from django.db import IntegrityError

from django.shortcuts import render, get_object_or_404
from membership.decorators import membership_required, sublibrarian_membership_required
from .models import Sublibrarian_param, DailyTransactionSub as DailyTransaction
from Library.user_auth import *
from librarian.models import Librarian_param
from studentsData.models import *
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from django.utils import timezone


@login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def sublibrarian_signup(request, *args, **kwargs):
    if request.method == "POST":
        try:
            phone = request.POST.get("phone")
            address = request.POST.get("address")

            librarian = Librarian_param.objects.get(user=request.user)

            sublibrarian = create_user_and_login(request)

            if sublibrarian:
                Sublibrarian_param.objects.create(
                    user=sublibrarian,
                    librarian=librarian,
                    sublibrarian_phone_num=phone,
                    sublibrarian_address=address,
                    is_sublibrarian=True,
                )
                group = Group.objects.get(name="sublibrarian")
                sublibrarian.groups.add(group)

                messages.success(
                    request,
                    f"Sublibrarian {sublibrarian.first_name} {sublibrarian.last_name} has been added successfully."
                )
                return redirect("/librarian/dashboard/")
            else:
                messages.error(request, "Failed to create user. Please try again.")
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    # For both GET requests and failed POST requests
    try:
        librarian = Librarian_param.objects.get(user=request.user)
        library_name = librarian.library_name
        return render(
            request,
            "addsub.html",
            {
                "librarian": librarian,
                "library_name": library_name
            }
        )
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect("/librarian/dashboard/")

@sublibrarian_membership_required("Advanced")
def sublibrarian_login(request, *args, **kwargs):
    if request.method == "POST":
        try:
            sublibrarian = authenticate_and_login(request, "sublibrarian")
            if sublibrarian:
                return redirect("/sublibrarian/dashboard/")
            else:
                messages.error(
                    request,
                    "Invalid credentials or you do not have sublibrarian access.",
                )
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")

    return render(request, "login.html", {"role": "sublibrarian"})


def sublibrarian_logout(request, *args, **kwargs):
    try:
        logout(request)
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")

    return redirect("/")

from membership.models import *
# @login_required(login_url="/sublibrarian/login/")
@sublibrarian_membership_required("Advanced")
def sublibrarian_profile(request, *args, **kwargs):
    user = request.user
    librarian = Sublibrarian_param.objects.get(user=user)
    library_man = librarian.librarian
    if user.is_authenticated and librarian.is_sublibrarian:
        try:
            lib_profile = librarian

            # Fetching Wallet
            try:
                wallet = Wallet.objects.get(user=library_man.user)

            except Wallet.DoesNotExist:
                wallet = None

            try:
                membership = Membership.objects.get(librarian=library_man)
            except Membership.DoesNotExist:
                membership = None

            try:
                transaction = Transaction.objects.filter(wallet=wallet)[::-1][:5]

            except Transaction.DoesNotExist:
                transaction = None

            return render(
                request,
                "user_profile.html",
                {
                    "lib": lib_profile,
                    "role": "sublibrarian",
                    "superlib": lib_profile.librarian,
                    "wallet": wallet,
                    "membership": membership,
                    "transaction": transaction,
                },
            )
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            lib_profile = Sublibrarian_param.objects.get(user=request.user)
            return render(
                request,
                "user_profile.html",
                {
                    "lib": lib_profile,
                    "role": "sublibrarian",

                },
            )

    return render(request, "unauthorized.html", {"role": "librarian"})



# @login_required(login_url="/sublibrarian/login/")
@sublibrarian_membership_required("Advanced")
def edit_sublibrarian_profile(request):
    try:
        # Fetch the logged-in user and their sublibrarian profile
        sublibrarian = request.user
        sublibrarian_profile = Sublibrarian_param.objects.get(user=sublibrarian)

        if request.method == "POST":
            # Get data from the form for the User model
            first_name = request.POST.get("first_name")
            last_name = request.POST.get("last_name")
            email = request.POST.get("email")

            # Get data for the Sublibrarian_param model
            phone = request.POST.get("phone")
            address = request.POST.get("address")

            try:
                # Update the User model fields
                sublibrarian.first_name = first_name
                sublibrarian.last_name = last_name
                sublibrarian.email = email
                sublibrarian.save()

                # Update the Sublibrarian_param model fields
                sublibrarian_profile.sublibrarian_phone_num = phone
                sublibrarian_profile.sublibrarian_address = address
                sublibrarian_profile.save()

                messages.success(request, "Profile updated successfully.")
                return redirect("/sublibrarian/profile/")  # Redirect to profile page

            except IntegrityError as e:
                error_message = str(e).lower()
                if "unique constraint" in error_message or "duplicate" in error_message:
                    if "email" in error_message:
                        messages.error(request, "A user with this email already exists.")
                    else:
                        messages.error(request, "A user with this information already exists.")
                else:
                    messages.error(request, "Database error occurred. Please try again.")

                # Return to the form with current data
                return render(
                    request,
                    "edit_profile.html",
                    {
                        "profile": sublibrarian_profile,
                        "sublibrarian": sublibrarian,
                        "role": "sublibrarian",
                    },
                )

        # Prepopulate the form with current data
        return render(
            request,
            "edit_profile.html",
            {
                "profile": sublibrarian_profile,
                "sublibrarian": sublibrarian,
                "role": "sublibrarian",
            },
        )
    except Sublibrarian_param.DoesNotExist:
        messages.error(request, "Sublibrarian profile not found.")
        return redirect(
            "/sublibrarian/login/"
        )  # Redirect to login if no profile is found
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect("/sublibrarian/edit-profile/")


# @login_required(login_url="/sublibrarian/login/")
# @membership_required("Basic")
@sublibrarian_membership_required("Advanced")
def library_data(request, *args, **kwargs):
    user = request.user
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        librarian = Librarian_param.objects.get(user=user)
        sublibrarian = None
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        try:
            s1 = StudentData.objects.filter(sublibrarian=sublibrarian)
            s2 = StudentData.objects.filter(librarian=sublibrarian.librarian)
            students = s1 | s2
        except Sublibrarian_param.DoesNotExist:
            students = StudentData.objects.filter(librarian=librarian)

        students_with_unpaid_fees = students.select_related("registrationfee").filter(
            registrationfee__is_paid=False
        )

        invoices = Invoice.objects.filter(student__in=students).select_related(
            "student"
        )

        invoice_data = {}
        for invoice in invoices:
            invoice_data[invoice.student_id] = {  # type: ignore
                "invoice": invoice,
                "shifts": invoice.shift.all(),
                "months": invoice.months.all(),
            }

        return render(
            request,
            "table.html",
            {
                "students": students,
                "students_with_fees": students_with_unpaid_fees,
                "invoice_data": invoice_data,
                "role": "sublibrarian",
                "location": location,
                "librarian": librarian,
            },
        )
    else:
        return render(request, "unauthorized.html", {"role": "sublibrarian"})

from datetime import timedelta
from django.utils import timezone
from django.utils.timezone import now
from django.db.models import Sum, Count
from django.db.models.functions import TruncMonth
from studentsData.utils import update_seat_availability

@login_required(login_url="/sublibrarian/login/")
@sublibrarian_membership_required("Advanced")
def dashboard(request):
    user = request.user
    librarian = Sublibrarian_param.objects.get(user=request.user)

    location = librarian.sublibrarian_address

    if user.is_authenticated and librarian.is_sublibrarian:
        # Get the current librarian's associated library

        # Fetch student data
        students_data = StudentData.objects.filter(librarian__user=user)

        # Fetch student data for the current month
        current_month_start = now().replace(day=1)
        current_month_end = now()

        s1  = StudentData.objects.filter(
            sublibrarian__user=request.user,
            registration_date__range=[current_month_start, current_month_end],
        )
        s2  = StudentData.objects.filter(
            librarian=librarian.librarian,
            registration_date__range=[current_month_start, current_month_end],
        )
        student_data = s1 | s2
        student_count = student_data.count()

        # Fetch library address
        library_address = librarian.sublibrarian_address  # Adjust according to your model

        # Calculate registration count for the last month

        registration_count = student_data.count()

        # Calculate total invoice amount for the current month
        current_month_start = now().replace(day=1)
        total_invoice_amount = (
            Invoice.objects.filter(
                student__in=students_data, issue_date__gte=current_month_start
            ).aggregate(total_amount=Sum("total_amount"))["total_amount"]
            or 0
        )

        # Get the first day of the current month
        start_date = now().replace(day=1) - timedelta(days=1)
        # Get the first day of the month 6 months ago
        start_date = start_date - timedelta(days=5 * 30)  # Approximation of 6 months

        user_growth_data = (
            students_data.annotate(month=TruncMonth("registration_date"))
            .values("month")
            .annotate(user_count=Count("id"))
            .filter(month__gte=start_date)
            .order_by("month")
        )

        growth_months = [entry["month"].strftime("%B %Y") for entry in user_growth_data]
        user_counts = [entry["user_count"] for entry in user_growth_data]

        # State-wise student count with state names
        state_wise_student_count = (
            students_data.values("locality")
            .annotate(count=Count("id"))
            .order_by("state__name")
        )

        # expire_seat_count = update_seat_availability(librarian)
        context = {
                "role": "sublibrarian",
                "students_data": student_data,
                "students_count": student_count,
                "library_address": library_address,
                "registration_count": registration_count,
                "total_invoice_amount": total_invoice_amount,
                "growth_months": growth_months,
                "user_counts": user_counts,
                "state_wise_student_count": state_wise_student_count,
                "location": location,
                "lib": librarian,
            }
        return render(
            request,
            "dashboard.html",
            context,
        )
    else:
        return render(request, "unauthorized.html", {"role": "sublibrarian"})




@login_required(login_url="/sublibrarian/login/")
def help_page(request):
    return render(request, "help.html", {"role": "sublibrarian"})


@login_required(login_url="/sublibrarian/login/")
def feedback_page(request):
    return render(request, "feedback.html", {"role": "sublibrarian"})


def page_not_found_view(request, exception=None):
    return render(request, "404.html", {"role": "sublibrarian"})



def get_int_value(post_data, key, default=0):
    value = post_data.get(key, "")
    return int(value) if value else default

from django.core.exceptions import ObjectDoesNotExist

# @login_required(login_url="/sublibrarian/login/")
# @membership_required("Advanced")
@sublibrarian_membership_required("Advanced")
def daily_transactions(request, *args, **kwargs):
    user = request.user
    librarian = Sublibrarian_param.objects.get(user=user)
    location = librarian.sublibrarian_address
    if user.is_authenticated and librarian.is_sublibrarian:
        if request.method == "POST":
            try:
                date = datetime.now().date()
                opening_balance = get_int_value(request.POST, "opening_balance")
                other_income_cash = get_int_value(request.POST, "other_income_cash")
                deposit = get_int_value(request.POST, "deposit")
                sales_return_cash = get_int_value(request.POST, "sales_return_cash")
                other_expenses_cash = get_int_value(request.POST, "other_expenses_cash")

                yesterday_date = datetime.now().date() - timedelta(days=1)
                try:
                    yesterday_transaction = DailyTransaction.objects.get(
                        date=yesterday_date
                    )
                except DailyTransaction.DoesNotExist:
                    yesterday_transaction = DailyTransaction.objects.filter(
                        sublibrarian=librarian
                    ).last()

                if opening_balance:
                    opening_balance = opening_balance
                else:
                    opening_balance = yesterday_transaction.closing_balance_cash  # type: ignore

                cash_payment = 0
                online_payment = 0
                total_student_fees_collection = 0
                total_invoice_collection = 0

                invoices_data = Invoice.objects.filter(
                    issue_date=datetime.now().date(), student__sublibrarian=librarian
                )

                for invoice in invoices_data:
                    if invoice.mode_pay == "cash":
                        cash_payment += invoice.total_amount
                    else:
                        online_payment += invoice.total_amount

                    try:
                        registration_fee = RegistrationFee.objects.get(
                            student=invoice.student
                        )
                        if (
                            registration_fee.is_paid
                            and registration_fee.student.registration_date == date
                        ):

                            total_student_fees_collection += (
                                registration_fee.student.registration_fee
                            )

                    except RegistrationFee.DoesNotExist:
                        pass

                    total_invoice_collection += (
                        invoice.total_amount - total_student_fees_collection
                    )

                sales_cash = cash_payment
                sales_online = online_payment
                other_income_online = 0
                sales_return_online = 0
                other_expenses_online = 0
                transaction_count = invoices_data.count()
                total_daily_collection = (int(sales_cash) + int(sales_online)) + (
                    int(other_income_cash) + int(other_income_online)
                )

                closing_balance_online = (
                    opening_balance
                    + sales_cash
                    + sales_online
                    + other_income_cash
                    + other_income_online
                    - deposit
                    - sales_return_cash
                    - sales_return_online
                    - other_expenses_cash
                    - other_expenses_online
                )
                closing_balance_cash = (
                    opening_balance
                    + sales_cash
                    + other_income_cash
                    - deposit
                    - sales_return_cash
                    - other_expenses_cash
                )

                transactions = DailyTransaction.objects.create(
                    sublibrarian=librarian,
                    date=date,
                    opening_balance=opening_balance,
                    closing_balance_online=closing_balance_online,
                    closing_balance_cash=closing_balance_cash,
                    sales_cash=sales_cash,
                    sales_online=sales_online,
                    deposit=deposit,
                    other_income_cash=other_income_cash,
                    other_income_online=other_income_online,
                    sales_return_cash=sales_return_cash,
                    sales_return_online=sales_return_online,
                    other_expenses_cash=other_expenses_cash,
                    other_expenses_online=other_expenses_online,
                    transaction_count=transaction_count,
                    total_student_fees_collection=total_student_fees_collection,
                    total_invoice_collection=total_invoice_collection,
                    total_daily_collection=total_daily_collection,
                )

                messages.success(
                    request,
                    f"Daily transaction for {date} has been created successfully.",
                )

                return redirect("/sublibrarian/daily-transaction/")
            except Exception as e:
                messages.error(request, f"An error occurred: {str(e)}")
        try:
            # Retrieve all transactions for the given librarian, sort them in reverse order, and get the first one
            last_transaction = DailyTransaction.objects.all().last()
        except IndexError:
            # Handle the case where no transactions are found
            last_transaction = None
        except ObjectDoesNotExist:
            # Handle any potential ObjectDoesNotExist exceptions
            last_transaction = None
        return render(
            request,
            "daily_transactions.html",
            {
                "role": "sublibrarian",
                "location": location,
                "transactions": last_transaction,
            },
        )
    else:
        # messages.error(request, "You are not authorized to view this page.")
        return render(request, "unauthorized.html", {"role": "sublibrarian"})


# @membership_required("Basic")
@sublibrarian_membership_required("Advanced")
def shifts_update(request, pk):
    shift = get_object_or_404(Shift, pk=pk)
    if request.method == "POST":
        # Get the updated values
        new_name = request.POST.get("name")
        new_time_range = request.POST.get("time")
        new_price = request.POST.get("price")

        # Save the old values for comparison (if needed)
        old_name = shift.name

        # Update the shift object
        shift.name = new_name
        shift.time_range = new_time_range
        shift.price = new_price
        shift.save()

        # Update related seats if needed (e.g., if seat naming depends on shift name)
        if new_name != old_name:
            related_seats = Seat.objects.filter(shift=shift)
            for seat in related_seats:
                # Example: Updating seat names with the new shift name
                seat.seat_number = seat.seat_number.replace(old_name, new_name)
                seat.save()

        messages.success(
            request, "Shift updated successfully, and related seats were updated."
        )
        return redirect("/sublibrarian/shifts")

    return render(request, "shifts_list.html", {"shift": shift, "role": "sublibrarian"})


def shifts_delete(request, pk):
    shift = get_object_or_404(Shift, pk=pk)
    shift.delete()
    messages.info(request, "Shift deleted successfully")
    return redirect("/sublibrarian/shifts")

@membership_required("Basic")
def shifts_create(request):
    user = request.user
    sub = Sublibrarian_param.objects.get(user=user)
    librarian = Librarian_param.objects.get(user=sub.librarian.user)
    location = librarian.librarian_address

    if user.is_authenticated and librarian.is_librarian:
        if request.method == "POST":
            name = request.POST.get("name")
            time_range = request.POST.get("time")
            price = request.POST.get("price")

            # Create the new shift
            new_shift = Shift.objects.create(
                librarian=librarian,
                name=name,
                time_range=time_range,
                price=price,
            )

            # Fetch existing shifts for the librarian
            existing_shifts = Shift.objects.filter(librarian=librarian).exclude(
                id=new_shift.id
            )

            # If there are existing shifts, check for seats to duplicate
            if existing_shifts.exists():
                # Find any seats from the existing shifts
                existing_seats = Seat.objects.filter(shift__in=existing_shifts)

                if existing_seats.exists():
                    # Duplicate the seats from the existing shifts to the new shift
                    for seat in existing_seats:
                        # Check if a seat with the same number already exists for the new shift
                        if not Seat.objects.filter(
                            seat_number=seat.seat_number,
                            librarian=librarian,
                            shift=new_shift,
                        ).exists():
                            # Create a new seat only if it does not already exist
                            Seat.objects.create(
                                seat_number=seat.seat_number,
                                librarian=librarian,
                                shift=new_shift,
                            )

            messages.success(request, "Shift created successfully")
            return redirect("/sublibrarian/shifts")

        # Fetch all shifts for display
        shifts = Shift.objects.filter(librarian=librarian)

        return render(
            request,
            "shifts_list.html",
            {"shifts": shifts, "role": "sublibrarian", "location": location},
        )
    else:
        # If user is not authorized
        return render(request, "unauthorized.html", {"role": "sublibrarian"})

from django.db.models import Prefetch
@sublibrarian_membership_required("Advanced")
def create_seat(request):
    user = request.user
    sub = Sublibrarian_param.objects.get(user=user)
    librarian = Librarian_param.objects.get(user=sub.librarian.user)
    shifts = Shift.objects.filter(librarian=librarian)

    if request.method == "POST":
        total_seats = int(request.POST.get("total_seats", 0))
        prefix = request.POST.get("prefix", "")

        # Fetch all shifts for the librarian

        for shift in shifts:
            # Check for existing seats with the same prefix
            existing_seats = Seat.objects.filter(
                seat_number__startswith=prefix, librarian=librarian, shift=shift
            )

            # Find the maximum number used in the existing seats
            existing_numbers = []
            for seat in existing_seats:
                # Extract the number part from the seat_number
                try:
                    num_part = int(
                        seat.seat_number[len(prefix) + 1 :]
                    )  # Get the number after the prefix and hyphen
                    existing_numbers.append(num_part)
                except ValueError:
                    continue  # If conversion fails, skip

            # Determine the starting number for new seats
            starting_number = max(existing_numbers, default=0) + 1

            # Ensure the starting number is at least 1
            starting_number = max(starting_number, 1)

            # Create seats with the given prefix and sequential numbers
            for i in range(starting_number, starting_number + total_seats):
                seat_number = f"{prefix}-{i}"  # Generate seat number with prefix
                Seat.objects.get_or_create(
                    seat_number=seat_number, librarian=librarian, shift=shift
                )

        return redirect("seats-list")

    seat_list = (
        Seat.objects.filter(librarian=librarian)
        .select_related("shift")
        .prefetch_related(
            Prefetch("booking_set", queryset=Booking.objects.select_related("student"))
        )
    ).order_by("shift")

    seat_data = []

    # Loop through each seat to gather booking data
    for seat in seat_list:
        # Extract prefix from seat number (e.g., "A-1" -> "A")
        prefix = seat.seat_number.split('-')[0] if '-' in seat.seat_number else seat.seat_number

        if seat.booking_set.exists():
            # Add each booking associated with the seat
            for booking in seat.booking_set.all():
                seat_data.append(
                    {
                        "id": seat.id,
                        "seat_number": seat.seat_number,
                        "prefix": prefix,
                        "shift": seat.shift,
                        "student_name": booking.student.name,
                        "booking_date": booking.booking_date,
                        "expire_date": booking.expire_date,
                    }
                )
        else:
            # Add seat with no booking information
            seat_data.append(
                {
                    "id": seat.id,
                    "seat_number": seat.seat_number,
                    "prefix": prefix,
                    "shift": seat.shift,
                    "student_name": None,
                    "booking_date": None,
                    "expire_date": None,
                }
            )

    # Pass seat_list and seat_data to the template
    return render(
        request,
        "create_seat.html",
        {
            "shifts": shifts,
            "seat_list": seat_list,
            "seat_data": seat_data,  # Pass the constructed seat data to the template
            "role": "sublibrarian",
        },
    )

@sublibrarian_membership_required("Advanced")
def update_seat(request, pk):
    # Get the original seat instance to know the initial seat number and librarian
    seat = get_object_or_404(Seat, pk=pk)
    original_seat_number = seat.seat_number
    original_librarian = seat.librarian

    if request.method == "POST":
        new_seat_number = request.POST.get("seat_number")
        new_librarian_id = request.POST.get("librarian")
        new_librarian = get_object_or_404(User, pk=new_librarian_id)

        # Update all seats that match the original seat number and librarian across shifts
        Seat.objects.filter(
            seat_number=original_seat_number, librarian=original_librarian
        ).update(seat_number=new_seat_number, librarian=new_librarian)

        return redirect("seat_list_sub")

    librarians = User.objects.all()
    shifts = Shift.objects.all()
    return render(
        request,
        "yourapp/update_seat.html",
        {"seat": seat, "librarian": librarians, "shifts": shifts},
    )


def cancel_seat(request, pk):
    seat = get_object_or_404(Seat, pk=pk)
    seat.is_available = True
    seat.save()
    return redirect("seats-list_sub")

def delete_seat(request, pk):
    seat = get_object_or_404(Seat, pk=pk)
    seat.delete()
    return redirect("seats-list_sub")


# Register Views for Sublibrarian
@login_required(login_url="/sublibrarian/login/")
def register_page(request, *args, **kwargs):
    """Main register page with two options: Student Register and Invoice Register"""
    user = request.user
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        return render(request, "register.html", {"role": "sublibrarian"})
    except Sublibrarian_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "sublibrarian"})


@login_required(login_url="/sublibrarian/login/")
def student_register(request, *args, **kwargs):
    """Student register page showing all students in tabular/card format"""
    user = request.user
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        # Get students for this sublibrarian
        students = StudentData.objects.filter(sublibrarian=sublibrarian).order_by('-registration_date')

        return render(request, "student_register.html", {
            "role": "sublibrarian",
            "students": students
        })
    except Sublibrarian_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "sublibrarian"})


@login_required(login_url="/sublibrarian/login/")
def invoice_register(request, *args, **kwargs):
    """Invoice register page showing all invoices for this sublibrarian"""
    user = request.user
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        # Get students for this sublibrarian
        students = StudentData.objects.filter(sublibrarian=sublibrarian)

        # Get all invoices for these students
        all_invoices = Invoice.objects.filter(student__in=students).select_related('student').prefetch_related('shift', 'months').order_by('-issue_date')

        # Prepare invoice data with related information
        invoice_data = []
        for invoice in all_invoices:
            shifts = invoice.shift.all()
            months = invoice.months.all()

            # Determine status based on due date and payment status
            status = 'pending'
            if hasattr(invoice, 'payment_status') and invoice.payment_status == 'Confirm':
                status = 'paid'
            elif invoice.due_date < timezone.now().date():
                status = 'overdue'

            invoice_data.append({
                'id': invoice.invoice_id,
                'slug': invoice.slug,
                'student_name': invoice.student.name,
                'student_id': invoice.student.unique_id,
                'student_course': invoice.student.course.name if invoice.student.course else 'N/A',
                'issue_date': invoice.issue_date,
                'due_date': invoice.due_date,
                'total_amount': invoice.total_amount,
                'discount_amount': invoice.discount_amount or 0,
                'status': status,
                'payment_status': getattr(invoice, 'payment_status', 'Pending'),
                'description': invoice.description or f"Library Fee - {', '.join([month.name for month in months])}",
                'shifts': [shift.name for shift in shifts],
                'months': [month.name for month in months],
                'mode_pay': getattr(invoice, 'mode_pay', 'Not specified')
            })

        return render(request, "invoice_register.html", {
            "role": "sublibrarian",
            "all_invoices": invoice_data,
            "total_invoices": len(invoice_data)
        })
    except Sublibrarian_param.DoesNotExist:
        return render(request, "unauthorized.html", {"role": "sublibrarian"})


@login_required(login_url="/sublibrarian/login/")
def get_student_invoices(request, student_slug):
    """API endpoint to fetch real invoice data for a student"""
    user = request.user
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        # Get the student
        student = StudentData.objects.get(slug=student_slug, sublibrarian=sublibrarian)

        # Get all invoices for this student
        invoices = Invoice.objects.filter(student=student).order_by('-issue_date')

        # Prepare invoice data
        invoice_data = []
        for invoice in invoices:
            # Get shifts and months for this invoice
            shifts = invoice.shift.all()
            months = invoice.months.all()

            invoice_data.append({
                'id': invoice.invoice_id,
                'slug': invoice.slug,
                'date': invoice.issue_date.strftime('%Y-%m-%d'),
                'due_date': invoice.due_date.strftime('%Y-%m-%d'),
                'amount': float(invoice.total_amount),
                'discount_amount': float(invoice.discount_amount or 0),
                'status': 'overdue' if invoice.due_date < timezone.now().date() else 'pending',
                'description': invoice.description or f"Library Fee - {', '.join([month.name for month in months])}",
                'shifts': [shift.name for shift in shifts],
                'months': [month.name for month in months],
                'mode_pay': invoice.mode_pay or 'Not specified'
            })

        return JsonResponse({
            'success': True,
            'invoices': invoice_data,
            'student': {
                'name': student.name,
                'unique_id': student.unique_id,
                'slug': student.slug
            }
        })

    except Sublibrarian_param.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Unauthorized'})
    except StudentData.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Student not found'})