importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.6.3/firebase-messaging.js");

firebase.initializeApp({
  apiKey: "AIzaSyBJSLcv459JwCzNhkgOndDQ7wzuAx81YwU",
  authDomain: "shashtrarth-fcm.firebaseapp.com",
  projectId: "shashtrarth-fcm",
  storageBucket: "shashtrarth-fcm.firebasestorage.app",
  messagingSenderId: "826207026444",
  appId: "1:826207026444:web:ea2f8a6287c93ecebc1d29",
  measurementId: "G-Y8GGEV9RWX"
});

const messaging = firebase.messaging();

messaging.setBackgroundMessageHandler(function (payload) {
  console.log("Received background message ", payload);

  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: payload.notification.icon,
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});
