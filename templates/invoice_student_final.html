<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice.student.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa, #e0e4ff, #fceefc, #eefafc);
            background-size: 400% 400%;
            animation: pearlGradient 15s ease infinite;
            position: relative;
            overflow-x: hidden;
        }

        @keyframes pearlGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .invoice-container {
            max-width: 1265px;
            margin: auto;
            background: #fff;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
            background-position: top right;
            background-repeat: no-repeat;
        }

        .header, .section, .footer { margin-bottom: 1.5rem; }
        .header h2 { margin-bottom: 0.5rem; }
        .header .library-name {
            font-size: 2.5rem;
            font-weight: 900;
            color: #4f46e5;
            margin-top: 0.25rem;
        }
        .header .library-logo {
            width: 100%;
            aspect-ratio: 5 / 1;
            object-fit: contain;
            margin-bottom: 0.5rem;
        }
        .section h5 { margin-bottom: 1rem; border-bottom: 1px solid #dee2e6; padding-bottom: 0.5rem; }
        .info { margin-bottom: 0.5rem; }
        .info span { display: inline-block; min-width: 120px; font-weight: 600; }
        table { width: 100%; margin-top: 1rem; }
        th, td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #dee2e6; }
        .text-end { text-align: end; }
        .btn { margin-right: 0.5rem; }

        .green-thank-you {
            background-color: #e6f4ea;
            color: #1b5e20;
            padding: 1.25rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 0.95rem;
            font-weight: 500;
            position: relative;
        }
        .green-thank-you::before {
            content: "\1F331";
            font-size: 1.5rem;
            position: absolute;
            top: 0.75rem;
            left: 1rem;
        }

        .student-photo {
            float: right;
            width: 150px;
            height: 200px;
            object-fit: cover;
            background-color: #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            border: 1px solid black;
            box-shadow: 0 0 0 4px #fff, 0 0 0 5px black;
        }

        .gmap-callout {
            margin-top: 1rem;
            background-color: #e8f5e9;
            color: #1b5e20;
            padding: 1rem;
            border-left: 5px solid #43a047;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        .gmap-callout a {
            color: #1b5e20;
            text-decoration: underline;
            font-weight: bold;
        }
    </style>
        <script type="application/ld+json">
        {
          "@context": "http://schema.org",
          "@type": "Invoice",
          "paymentDue": "{{ invoice.due_date|date:'Y-m-d' }}",
          "paymentStatus": "http://schema.org/PaymentComplete",
          "paymentMethod": "{{ invoice.mode_pay }}",
          "totalPaymentDue": {
            "@type": "PriceSpecification",
            "price": "{{ invoice.total_amount }}",
            "priceCurrency": "INR"
          },
          "provider": {
            "@type": "Organization",
            "name": "{{ invoice.student.librarian.library_name }}",
            "email": "{{ invoice.student.librarian.email }}",
            "url": "{{ invoice.student.librarian.website }}"
          },
          "customer": {
            "@type": "Person",
            "name": "{{ invoice.student.name }}"
          }
        }
    </script>
</head>
<body>
<div class="invoice-container">
    <div class="header text-center">
        {% if invoice.student.librarian.image %}
        <img src="{{ invoice.student.librarian.image.url }}" alt="Library Logo" class="library-logo">
        {% endif %}
        <h2>Payment Receipt</h2>
        <div class="library-name">{{ invoice.student.librarian.library_name }}</div>
    </div>

    <div class="green-thank-you">
        🌱 Thank you for choosing e-invoicing and contributing to environmental protection. Your action helps reduce paper usage and save trees!
    </div>

    <div class="section">
        <h5>Student Details</h5>
        {% if invoice.student.image %}
        <img src="{{ invoice.student.image.url }}" alt="Student Photo" class="student-photo">
        {% else %}
        <div class="student-photo">{{ invoice.student.name|first|upper }}</div>
        {% endif %}
        <div class="info"><span>Name:</span> {{ invoice.student.name }}</div>
        <div class="info"><span>Course:</span> {{ invoice.student.course }}</div>
        <div class="info"><span>Email:</span> {{ invoice.student.email }}</div>
        <div class="info"><span>Mobile:</span> {{ invoice.student.mobile }}</div>
    </div>

    <div class="section">
        <h5>Library Details</h5>
        <div class="info"><span>Library:</span> {{ invoice.student.librarian.library_name }}</div>
        <div class="info"><span>Address:</span> {{ invoice.student.librarian.librarian_address }}</div>
        <div class="info"><span>Contact:</span> {{ invoice.student.librarian.phone_num }}</div>
        <div class="info"><span>Email:</span> {{ invoice.student.librarian.email }}</div>
        <div class="info"><span>Website:</span> {{ invoice.student.librarian.website }}</div>
        <div class="info">
            <span>Location:</span>
            <a href="https://www.google.com/maps/search/?api=1&query={{ invoice.student.librarian.librarian_address|urlencode }}" target="_blank">View on Google Maps</a>
        </div>

        <div class="gmap-callout">
            🌍 Do you like the service? What did you like the most? <a href="https://www.google.com/maps/search/?api=1&query={{ invoice.student.librarian.librarian_address|urlencode }}" target="_blank">Tell us on Google Maps</a> — this helps us reach more students!
        </div>
    </div>

    <div class="section">
        <h5>Invoice Info</h5>
        <div class="info"><span>Invoice No.:</span> #{{ invoice.invoice_id|default:invoice.id }}</div>
        <div class="info"><span>Issue Date:</span> {{ invoice.issue_date }}</div>
        <div class="info"><span>Next Payment:</span> {{ invoice.due_date }}</div>
        <div class="info"><span>Month Ended On:</span> {{ invoice.due_date }}</div>
        <div class="info"><span>Mode:</span> {{ invoice.mode_pay|title }}</div>
        <div class="info"><span>Status:</span> Paid</div>
    </div>

    <div class="section">
        <h5>Payment Breakdown</h5>
        <table>
            <thead>
                <tr><th>Description</th><th>Period</th><th class="text-end">Amount</th></tr>
            </thead>
            <tbody>
                {% for shift in invoice.shifts.all %}
                <tr>
                    <td>{{ shift.name }}</td>
                    <td>
                        {% for month in invoice.months.all %}{{ month.name }}{% if not forloop.last %}, {% endif %}{% endfor %}
                    </td>
                    <td class="text-end">₹{{ shift.price }}</td>
                </tr>
                {% endfor %}
                {% if invoice.discount_amount %}
                <tr><td>Discount</td><td>-</td><td class="text-end">-₹{{ invoice.discount_amount }}</td></tr>
                {% endif %}
                <tr><td colspan="2"><strong>Total</strong></td><td class="text-end"><strong>₹{{ invoice.total_amount }}</strong></td></tr>
            </tbody>
        </table>
    </div>

    <div class="footer text-center">
        <p>Thank you for your payment!</p>
        <p>This is a computer-generated receipt. For queries: <EMAIL></p>
        <p style="font-size: 0.8rem;">Generated on {{ invoice.issue_date }}</p>
    </div>

    <div class="text-center no-print">
        <button class="btn btn-success" onclick="downloadInvoice()">Download</button>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    function downloadInvoice() {
        const invoiceContainer = document.querySelector('.invoice-container');
        html2canvas(invoiceContainer, {
            scale: 2,
            useCORS: true
        }).then(canvas => {
            const link = document.createElement('a');
            link.download = `invoice_{{ invoice.invoice_id|default:invoice.id }}.png`;
            link.href = canvas.toDataURL();
            link.click();
        });
    }
</script>
</body>
</html>
