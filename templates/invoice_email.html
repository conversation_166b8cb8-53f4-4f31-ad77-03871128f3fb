{% extends "email_base.html" %}

{% block email_title %}Invoice Generated - {{ library_name|default:'Librainian' }}{% endblock %}

{% block email_subject %}Invoice #{{ invoice_number }} - Payment Confirmation{% endblock %}

{% block email_description %}Invoice confirmation and payment details for library services subscription.{% endblock %}

{% block preview_text %}Invoice #{{ invoice_number }} for ₹{{ total_amount }}. Payment successful. Download your invoice.{% endblock %}

{% block header_icon %}📄{% endblock %}

{% block email_header_title %}Invoice Generated{% endblock %}

{% block email_header_subtitle %}Invoice #{{ invoice_number }} - Payment Successful{% endblock %}
{% block extra_css %}
<style>
    /* Invoice-specific styles */
    .invoice-header {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        margin: 20px 0;
        text-align: center;
    }

    .invoice-number {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 10px 0;
        font-family: 'Comfortaa', sans-serif;
    }

    .invoice-date {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
    }

    .amount-section {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border: 2px solid #22c55e;
        border-radius: 16px;
        padding: 30px;
        text-align: center;
        margin: 25px 0;
        position: relative;
        overflow: hidden;
    }

    .amount-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(34, 197, 94, 0.1), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    .amount-label {
        font-size: 16px;
        color: #166534;
        font-weight: 600;
        margin: 0 0 10px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        z-index: 2;
    }

    .amount-value {
        font-size: 42px;
        font-weight: 800;
        color: #059669;
        margin: 0;
        font-family: 'Comfortaa', sans-serif;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
    }

    .payment-status {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 14px;
        margin: 15px 0;
        position: relative;
        z-index: 2;
    }

    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .invoice-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        color: #374151;
        font-weight: 600;
        padding: 15px;
        text-align: left;
        border-bottom: 2px solid #e5e7eb;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .invoice-table td {
        padding: 15px;
        border-bottom: 1px solid #f3f4f6;
        color: #4b5563;
        vertical-align: top;
    }

    .invoice-table tr:last-child td {
        border-bottom: none;
    }

    .invoice-table tr:hover {
        background-color: #f9fafb;
    }

    .total-row {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        font-weight: 700;
        color: #0c4a6e;
    }

    .total-row td {
        border-top: 2px solid #0ea5e9;
        font-size: 16px;
    }

    .download-section {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 1px solid #f59e0b;
        border-radius: 12px;
        padding: 25px;
        text-align: center;
        margin: 25px 0;
    }

    .download-title {
        font-size: 18px;
        font-weight: 600;
        color: #92400e;
        margin: 0 0 15px 0;
        font-family: 'Comfortaa', sans-serif;
    }

    .download-text {
        color: #a16207;
        margin: 0 0 20px 0;
        font-size: 14px;
    }

    .download-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white !important;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.3);
    }

    .download-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.4);
    }

    .billing-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 25px 0;
    }

    .billing-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 20px;
    }

    .billing-card h4 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 15px 0;
        font-family: 'Comfortaa', sans-serif;
    }

    .billing-card p {
        margin: 5px 0;
        color: #4b5563;
        font-size: 14px;
    }

    @media only screen and (max-width: 600px) {
        .billing-info {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .amount-value {
            font-size: 32px;
        }

        .invoice-table th,
        .invoice-table td {
            padding: 10px 8px;
            font-size: 12px;
        }
    }
</style>
{% endblock %}
{% block email_content %}
<h2 class="greeting">Hello {{ customer_name|default:'Valued Customer' }},</h2>
<p class="message">
    Thank you for your payment! Your invoice has been generated successfully.
    Please find the details below and download your invoice for your records.
</p>

<!-- Invoice Header -->
<div class="invoice-header">
    <h3 class="invoice-number">Invoice #{{ invoice_number }}</h3>
    <p class="invoice-date">Generated on {{ invoice_date|date:"F j, Y" }}</p>
</div>

<!-- Amount Section -->
<div class="amount-section">
    <p class="amount-label">Total Amount Paid</p>
    <p class="amount-value">₹{{ total_amount }}</p>
    <div class="payment-status">
        ✅ Payment Successful
    </div>
</div>

<!-- Invoice Details -->
<div class="info-card">
    <h3 class="card-title">📋 Invoice Details</h3>

    <table class="invoice-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Period</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for item in invoice_items %}
            <tr>
                <td>{{ item.description|default:'Library Subscription' }}</td>
                <td>{{ item.period|default:'Monthly' }}</td>
                <td>₹{{ item.amount }}</td>
            </tr>
            {% empty %}
            <tr>
                <td>Library Subscription</td>
                <td>{{ subscription_period|default:'Monthly' }}</td>
                <td>₹{{ total_amount }}</td>
            </tr>
            {% endfor %}

            {% if discount_amount %}
            <tr>
                <td>Discount Applied</td>
                <td>-</td>
                <td>-₹{{ discount_amount }}</td>
            </tr>
            {% endif %}

            {% if tax_amount %}
            <tr>
                <td>Tax ({{ tax_rate|default:'18' }}%)</td>
                <td>-</td>
                <td>₹{{ tax_amount }}</td>
            </tr>
            {% endif %}

            <tr class="total-row">
                <td><strong>Total Amount</strong></td>
                <td><strong>-</strong></td>
                <td><strong>₹{{ total_amount }}</strong></td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Billing Information -->
<div class="billing-info">
    <div class="billing-card">
        <h4>📍 Billing Address</h4>
        <p><strong>{{ customer_name|default:'Customer Name' }}</strong></p>
        <p>{{ billing_address|default:'Address not provided' }}</p>
        <p>{{ billing_city|default:'' }} {{ billing_state|default:'' }} {{ billing_pincode|default:'' }}</p>
        {% if billing_phone %}
        <p>📞 {{ billing_phone }}</p>
        {% endif %}
    </div>

    <div class="billing-card">
        <h4>💳 Payment Information</h4>
        <p><strong>Payment Method:</strong> {{ payment_method|default:'Online Payment' }}</p>
        <p><strong>Transaction ID:</strong> {{ transaction_id|default:'N/A' }}</p>
        <p><strong>Payment Date:</strong> {{ payment_date|date:"F j, Y g:i A" }}</p>
        <p><strong>Status:</strong> <span style="color: #059669; font-weight: 600;">Completed</span></p>
    </div>
</div>

<!-- Download Section -->
<div class="download-section">
    <h3 class="download-title">📄 Download Your Invoice</h3>
    <p class="download-text">
        Click the button below to download a PDF copy of your invoice for your records.
    </p>
    <a href="{{ invoice_download_url|default:'#' }}" class="download-button">
        📥 Download Invoice PDF
    </a>
</div>

<!-- Additional Information -->
<div class="info-card">
    <h3 class="card-title">ℹ️ Important Information</h3>

    <div class="detail-row">
        <span class="detail-label">Library Name</span>
        <span class="detail-value">{{ library_name|default:'Librainian Library' }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Subscription Type</span>
        <span class="detail-value">{{ subscription_type|default:'Premium' }}</span>
    </div>

    <div class="detail-row">
        <span class="detail-label">Valid Until</span>
        <span class="detail-value">{{ subscription_end_date|date:"F j, Y" }}</span>
    </div>

    {% if next_billing_date %}
    <div class="detail-row">
        <span class="detail-label">Next Billing</span>
        <span class="detail-value">{{ next_billing_date|date:"F j, Y" }}</span>
    </div>
    {% endif %}
</div>

<p style="margin: 30px 0 0 0; color: #6b7280; font-size: 14px; text-align: center;">
    Thank you for choosing Librainian! If you have any questions about this invoice, please contact our support team.
</p>
{% endblock %}

