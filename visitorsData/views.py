# views.py

from django.http import Http404
from django.shortcuts import render, redirect, get_object_or_404

from membership.decorators import membership_required
from .models import Visitor
from librarian.models import *
from subLibrarian.models import *
from studentsData.models import Courses, RegistrationFee, Shift, States, StudentData
from django.contrib.auth.decorators import login_required
from django.utils.text import slugify
from django.utils.timezone import now


# List all blogs
@login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def visitor_list(request):
    try:
        # Check if the user is a sublibrarian
        sublibrarian = Sublibrarian_param.objects.get(user=request.user)
        # If the user is a sublibrarian, get the librarian's address through the sublibrarian
        location = sublibrarian.librarian.librarian_address
    except Sublibrarian_param.DoesNotExist:
        # If the user is not a sublibrarian, get the librarian's own address
        librarian = Librarian_param.objects.get(user=request.user)
        location = librarian.librarian_address

    # Handle POST request for form submission
    if request.method == "POST":
        name = request.POST.get("name")
        email = request.POST.get("email")
        contact = request.POST.get("phone")
        notes = request.POST.get("notes", "")
        purpose = request.POST.get("purpose", "")

        # Combine purpose into notes if provided
        if purpose:
            notes = f"Purpose: {purpose}\n{notes}".strip()

        librarian = None
        sublibrarian = None

        try:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
        except Sublibrarian_param.DoesNotExist:
            librarian = Librarian_param.objects.get(user=request.user)
            sublibrarian = None

        # Create visitor with required fields (without inqid first)
        visitor = Visitor(
            librarian=librarian,
            sublibrarian=sublibrarian,
            name=name,
            contact=int(contact) if contact else 0,
            email=email or "",
            notes=notes,
            status="pending",
        )

        # Generate unique inqid
        visitor.inqid = visitor.generate_inqid()
        visitor.slug = slugify(f"{visitor.name}-{visitor.inqid}")
        visitor.save()

        return redirect("/visitors/")

    if request.user.groups.exists():
        role = request.user.groups.all()[0].name

        print(role)

        # Check for Librarian or Sublibrarian role
        if role == "librarian":
            # Fetch visitors for librarian
            visitors = Visitor.objects.filter(librarian__user=request.user)[::-1]
        elif role == "sublibrarian":
            # Fetch visitors for sublibrarian
            visitors = Visitor.objects.filter(sublibrarian__user=request.user)[::-1]
        else:
            # Handle other roles or no specific role
            visitors = Visitor.objects.none()  # Empty QuerySet
    else:
        raise Http404("User does not belong to any role group")

    # Render the visitors to the template
    return render(
        request,
        "visitor_list.html",
        {"visitors": visitors, "role": role, "location": location},
    )

from blogs.models import Blog
# View a single blog
@login_required(login_url="/librarian/login/")
def visitor_detail(request, slug):
    role = request.user.groups.all()[0].name
    try:
        visitor = Visitor.objects.get(slug=slug)
    except Visitor.DoesNotExist:
        raise Http404("Visitor does not exist")
    return render(request, "visitor_details.html", {"visitor": visitor, "role": role})


# @login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def add_visitor(request):
    role = request.user.groups.all()[0].name
    if request.method == "POST":
        date = request.POST.get("date")
        name = request.POST.get("name")
        email = request.POST.get("email")
        contact = request.POST.get("phone")
        shift_ids = request.POST.getlist("shifts")
        note = request.POST.get("note")
        callback = request.POST.get("callback")
        status = request.POST.get("status")

        librarian = None
        sublibrarian = None

        shifts = Shift.objects.filter(id__in=shift_ids)
        try:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
        
        except Sublibrarian_param.DoesNotExist:
            librarian = Librarian_param.objects.get(user=request.user)
            sublibrarian = None

        visitor = Visitor.objects.create(
            librarian=librarian,
            sublibrarian=sublibrarian,
            date=date,
            name=name,
            contact=contact,
            email=email,
            notes=note,
            callback=callback,
            status=status,
        )
        visitor.shift.set(shifts)
        visitor.save()

        return redirect(
            "/visitors/"
        )  # Redirect to the visitors list page after successful save
    if Sublibrarian_param.objects.filter(user=request.user).exists():
        librarian = Sublibrarian_param.objects.get(user=request.user).librarian.user
    else:
        librarian = request.user
    shifts = Shift.objects.filter(librarian__user=librarian)
    return render(request, "visitor_create.html", {"shifts": shifts, "role": role})


@login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def edit_visitor(request, slug):
    role = request.user.groups.all()[0].name
    visitor = get_object_or_404(Visitor, slug=slug)
    librarian = None
    sublibrarian = None

    try:
        # Attempt to get the sublibrarian first
        sublibrarian = Sublibrarian_param.objects.get(user=request.user)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        try:
            # If no sublibrarian, try to get the librarian
            librarian = Librarian_param.objects.get(user=request.user)
        except Librarian_param.DoesNotExist:
            # Neither librarian nor sublibrarian
            pass

    if request.method == "POST":

        visitor.date = request.POST.get("date")
        visitor.name = request.POST.get("name")
        visitor.email = request.POST.get("email")
        visitor.contact = request.POST.get("phone")
        shift_id = request.POST.getlist("shifts")
        shifts = Shift.objects.filter(id__in=shift_id)
        visitor.callback = request.POST.get("callback")
        visitor.status = request.POST.get("status")
        visitor.librarian = librarian
        visitor.sublibrarian = sublibrarian

        visitor.shift.set(shifts)
        visitor.save()

        return redirect("/visitors/")

    shifts = Shift.objects.filter(librarian__user=request.user)
    # timings = Timing.objects.all()
    return render(
        request,
        "visitor_create.html",
        {"visitor": visitor, "shifts": shifts, "role": role},
    )


@login_required(login_url="/librarian/login/")
@membership_required("Advanced")
def delete_visitor(request, slug):
    visitor = get_object_or_404(Visitor, slug=slug)
    visitor.delete()
    return redirect("/visitors/")


def page_not_found_view(request, exception=None):
    return render(request, "404.html", status=404)
